#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证唯一性约束是否已经生效
"""

import os
import duckdb

def verify_constraints():
    """验证所有表的唯一性约束"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 验证唯一性约束")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    # 定义测试数据
    test_cases = {
        'contract_risk_details': {
            'unique_fields': ['algorithm_result_id', 'member_id', 'contract_name', 'detection_type', 'detection_method'],
            'test_data': (1, 'test_user', 'BTC_USDT', 'wash_trading', 'cross_account'),
            'insert_sql': """
                INSERT INTO contract_risk_details (
                    id, algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                    risk_level, risk_score, created_at
                ) VALUES (NULL, ?, ?, ?, ?, ?, 'HIGH', 0.8, CURRENT_TIMESTAMP)
            """
        },
        'same_account_wash_trading': {
            'unique_fields': ['user_id', 'contract_name', 'long_position_id', 'short_position_id'],
            'test_data': ('test_user', 'BTC_USDT', 'long_123', 'short_456'),
            'insert_sql': """
                INSERT INTO same_account_wash_trading (
                    id, wash_trading_id, user_id, contract_name, long_position_id, short_position_id, created_at
                ) VALUES (NULL, 1, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
        },
        'cross_account_wash_trading': {
            'unique_fields': ['user_a_id', 'user_b_id', 'contract_name', 'trade_a_id', 'trade_b_id'],
            'test_data': ('user_a', 'user_b', 'BTC_USDT', 'trade_123', 'trade_456'),
            'insert_sql': """
                INSERT INTO cross_account_wash_trading (
                    id, wash_trading_id, user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id, created_at
                ) VALUES (NULL, 1, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
        },
        'high_frequency_trading_details': {
            'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
            'test_data': (1, 'test_user', 'BTC_USDT'),
            'insert_sql': """
                INSERT INTO high_frequency_trading_details (
                    id, algorithm_result_id, user_id, contract_name, trade_count, created_at
                ) VALUES (NULL, ?, ?, ?, 100, CURRENT_TIMESTAMP)
            """
        },
        'funding_rate_arbitrage_details': {
            'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
            'test_data': (1, 'test_user', 'BTC_USDT'),
            'insert_sql': """
                INSERT INTO funding_rate_arbitrage_details (
                    id, algorithm_result_id, user_id, contract_name, funding_rate, created_at
                ) VALUES (NULL, ?, ?, ?, 0.01, CURRENT_TIMESTAMP)
            """
        }
    }
    
    try:
        conn = duckdb.connect(db_path)
        
        results = {}
        
        for table_name, config in test_cases.items():
            print(f"\n🧪 测试表: {table_name}")
            print("-" * 50)
            
            unique_fields = config['unique_fields']
            test_data = config['test_data']
            insert_sql = config['insert_sql']
            
            print(f"唯一性字段: {', '.join(unique_fields)}")
            print(f"测试数据: {test_data}")
            
            try:
                # 检查表是否存在
                check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                exists = conn.execute(check_sql).fetchone()
                
                if not exists:
                    print(f"❌ 表不存在")
                    results[table_name] = 'table_not_exists'
                    continue
                
                # 第一次插入（应该成功）
                conn.execute(insert_sql, test_data)
                print("✅ 第一次插入成功")
                
                # 第二次插入相同数据（应该失败）
                try:
                    conn.execute(insert_sql, test_data)
                    print("❌ 第二次插入成功 - 唯一性约束未生效！")
                    results[table_name] = 'constraint_failed'
                    
                    # 清理测试数据
                    placeholders = ', '.join(['?' for _ in unique_fields])
                    delete_sql = f"DELETE FROM {table_name} WHERE ({', '.join(unique_fields)}) = ({placeholders})"
                    conn.execute(delete_sql, test_data)
                    
                except Exception as e:
                    print(f"✅ 第二次插入失败 - 唯一性约束生效: {str(e)[:100]}...")
                    results[table_name] = 'constraint_working'
                    
                    # 清理测试数据
                    placeholders = ', '.join(['?' for _ in unique_fields])
                    delete_sql = f"DELETE FROM {table_name} WHERE ({', '.join(unique_fields)}) = ({placeholders})"
                    conn.execute(delete_sql, test_data)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                results[table_name] = f'test_error: {e}'
        
        # 显示总结
        print(f"\n" + "=" * 80)
        print("📊 测试结果总结:")
        print("-" * 40)
        
        working_count = 0
        for table_name, result in results.items():
            if result == 'constraint_working':
                status = "✅ 约束生效"
                working_count += 1
            elif result == 'constraint_failed':
                status = "❌ 约束失效"
            elif result == 'table_not_exists':
                status = "⚠️  表不存在"
            else:
                status = f"❌ 测试错误"
            
            print(f"{table_name:<35} {status}")
        
        print(f"\n总计: {working_count}/{len(test_cases)} 个表的唯一性约束正常工作")
        
        if working_count == len(test_cases):
            print("🎉 所有表的唯一性约束都已生效！")
            print("💡 现在可以安全地运行合约风险分析，不会产生重复数据")
        else:
            print("⚠️  部分表的唯一性约束有问题，需要进一步检查")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_constraints()
