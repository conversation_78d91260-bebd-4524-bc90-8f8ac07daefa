#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查contract_risk_details表的重复数据
"""

import os
import sys
import json
import duckdb
from datetime import datetime
from typing import Dict, List, Any

class SimpleDBManager:
    """简单的数据库管理器"""
    def __init__(self):
        # 查找数据库文件
        possible_paths = [
            'data/risk_analysis.duckdb',
            'backend/database/trading_analysis.db',
            'data/risk_analysis.db',
            'backend/data/risk_analysis.db',
            'risk_analysis.db'
        ]

        self.db_path = None
        for path in possible_paths:
            if os.path.exists(path):
                self.db_path = path
                break

        if not self.db_path:
            raise FileNotFoundError("找不到数据库文件，请确认数据库路径")

        print(f"使用数据库: {self.db_path}")

    def execute_sql(self, sql: str, params=None):
        """执行SQL查询"""
        try:
            conn = duckdb.connect(self.db_path)
            if params:
                result = conn.execute(sql, params)
            else:
                result = conn.execute(sql)

            # 获取列名和数据
            columns = [desc[0] for desc in result.description] if result.description else []
            rows = result.fetchall()
            conn.close()

            return [dict(zip(columns, row)) for row in rows]
        except Exception as e:
            print(f"SQL执行失败: {sql}, 错误: {e}")
            raise

# 创建数据库管理器实例
db_manager = SimpleDBManager()

def check_contract_risk_duplicates():
    """检查contract_risk_details表的重复数据"""
    print("🔍 检查contract_risk_details表的重复数据...")
    print("=" * 60)
    
    try:
        # 1. 检查表是否存在
        check_table_sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='contract_risk_details'"
        table_exists = db_manager.execute_sql(check_table_sql)
        
        if not table_exists:
            print("❌ contract_risk_details表不存在")
            return
        
        print("✅ contract_risk_details表存在")
        
        # 2. 获取总记录数
        total_count_sql = "SELECT COUNT(*) as total FROM contract_risk_details"
        total_result = db_manager.execute_sql(total_count_sql)
        total_records = total_result[0]['total'] if total_result else 0
        
        print(f"📊 总记录数: {total_records}")
        
        if total_records == 0:
            print("ℹ️  表中没有数据")
            return
        
        # 3. 检查基于唯一性约束的重复数据
        print("\n🔍 检查重复数据（基于唯一性约束字段）...")
        
        duplicate_sql = """
        SELECT 
            algorithm_result_id,
            member_id,
            contract_name,
            detection_type,
            detection_method,
            COUNT(*) as count,
            MIN(created_at) as first_created,
            MAX(created_at) as last_created,
            GROUP_CONCAT(id) as ids
        FROM contract_risk_details
        GROUP BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
        HAVING COUNT(*) > 1
        ORDER BY count DESC, last_created DESC
        """
        
        duplicates = db_manager.execute_sql(duplicate_sql)
        
        if not duplicates:
            print("✅ 没有发现重复数据")
            return
        
        duplicate_count = sum(dup['count'] - 1 for dup in duplicates)  # 减1因为每组保留一条
        print(f"⚠️  发现 {len(duplicates)} 组重复数据，总计 {duplicate_count} 条重复记录")
        
        # 4. 显示重复数据详情
        print(f"\n📋 重复数据详情（前10组）:")
        print("-" * 120)
        print(f"{'算法结果ID':<12} {'用户ID':<15} {'合约名称':<15} {'检测类型':<20} {'检测方法':<20} {'重复数':<8} {'ID列表'}")
        print("-" * 120)
        
        for i, dup in enumerate(duplicates[:10]):
            algorithm_result_id = str(dup['algorithm_result_id'])[:10] + "..." if len(str(dup['algorithm_result_id'])) > 10 else str(dup['algorithm_result_id'])
            member_id = str(dup['member_id'])[:13] + "..." if len(str(dup['member_id'])) > 13 else str(dup['member_id'])
            contract_name = str(dup['contract_name'] or '')[:13] + "..." if len(str(dup['contract_name'] or '')) > 13 else str(dup['contract_name'] or '')
            detection_type = str(dup['detection_type'] or '')[:18] + "..." if len(str(dup['detection_type'] or '')) > 18 else str(dup['detection_type'] or '')
            detection_method = str(dup['detection_method'] or '')[:18] + "..." if len(str(dup['detection_method'] or '')) > 18 else str(dup['detection_method'] or '')
            ids = str(dup['ids'])[:20] + "..." if len(str(dup['ids'])) > 20 else str(dup['ids'])
            
            print(f"{algorithm_result_id:<12} {member_id:<15} {contract_name:<15} {detection_type:<20} {detection_method:<20} {dup['count']:<8} {ids}")
        
        if len(duplicates) > 10:
            print(f"... 还有 {len(duplicates) - 10} 组重复数据")
        
        # 5. 按时间分析重复数据
        print(f"\n📅 按创建时间分析重复数据:")
        time_analysis_sql = """
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total_records,
            COUNT(DISTINCT algorithm_result_id || '|' || member_id || '|' || contract_name || '|' || detection_type || '|' || detection_method) as unique_records
        FROM contract_risk_details
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 10
        """
        
        time_analysis = db_manager.execute_sql(time_analysis_sql)
        
        print("-" * 50)
        print(f"{'日期':<12} {'总记录数':<10} {'唯一记录数':<12} {'重复率'}")
        print("-" * 50)
        
        for row in time_analysis:
            date = row['date']
            total = row['total_records']
            unique = row['unique_records']
            duplicate_rate = ((total - unique) / total * 100) if total > 0 else 0
            print(f"{date:<12} {total:<10} {unique:<12} {duplicate_rate:.1f}%")
        
        # 6. 按算法结果ID分析
        print(f"\n🔬 按算法结果ID分析:")
        algorithm_analysis_sql = """
        SELECT 
            algorithm_result_id,
            COUNT(*) as total_records,
            COUNT(DISTINCT member_id || '|' || contract_name || '|' || detection_type || '|' || detection_method) as unique_records,
            MIN(created_at) as first_created,
            MAX(created_at) as last_created
        FROM contract_risk_details
        GROUP BY algorithm_result_id
        HAVING COUNT(*) != COUNT(DISTINCT member_id || '|' || contract_name || '|' || detection_type || '|' || detection_method)
        ORDER BY total_records DESC
        LIMIT 5
        """
        
        algorithm_analysis = db_manager.execute_sql(algorithm_analysis_sql)
        
        if algorithm_analysis:
            print("-" * 80)
            print(f"{'算法结果ID':<15} {'总记录数':<10} {'唯一记录数':<12} {'重复率':<10} {'时间跨度'}")
            print("-" * 80)
            
            for row in algorithm_analysis:
                algorithm_id = str(row['algorithm_result_id'])
                total = row['total_records']
                unique = row['unique_records']
                duplicate_rate = ((total - unique) / total * 100) if total > 0 else 0
                time_span = f"{row['first_created'][:10]} ~ {row['last_created'][:10]}"
                print(f"{algorithm_id:<15} {total:<10} {unique:<12} {duplicate_rate:.1f}%{'':<5} {time_span}")
        else:
            print("✅ 各算法结果ID内部没有重复数据")
        
        # 7. 生成清理建议
        print(f"\n💡 清理建议:")
        print("-" * 40)
        
        if duplicate_count > 0:
            print(f"1. 发现 {duplicate_count} 条重复记录需要清理")
            print(f"2. 建议保留最新的记录（created_at最大的）")
            print(f"3. 可以运行去重工具进行自动清理")
            print(f"4. 建议在清理前先备份数据")
            
            # 生成清理SQL示例
            print(f"\n🔧 清理SQL示例（删除重复记录，保留最新的）:")
            print("```sql")
            print("WITH ranked_records AS (")
            print("    SELECT id,")
            print("           ROW_NUMBER() OVER (")
            print("               PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method")
            print("               ORDER BY created_at DESC, risk_score DESC")
            print("           ) as rn")
            print("    FROM contract_risk_details")
            print(")")
            print("DELETE FROM contract_risk_details")
            print("WHERE id IN (")
            print("    SELECT id FROM ranked_records WHERE rn > 1")
            print(");")
            print("```")
        else:
            print("✅ 数据质量良好，无需清理")
        
        # 8. 保存检查报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'table_name': 'contract_risk_details',
            'total_records': total_records,
            'duplicate_groups': len(duplicates),
            'duplicate_records': duplicate_count,
            'duplicate_rate': (duplicate_count / total_records * 100) if total_records > 0 else 0,
            'duplicates_sample': duplicates[:10],
            'time_analysis': time_analysis,
            'algorithm_analysis': algorithm_analysis
        }
        
        report_path = f"temp/contract_risk_duplicates_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 详细报告已保存到: {report_path}")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def quick_cleanup_duplicates():
    """快速清理重复数据（保留最新的记录）"""
    print("\n🧹 快速清理重复数据...")
    
    try:
        # 确认操作
        response = input("⚠️  这将删除重复记录，是否继续？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 用户取消操作")
            return
        
        # 执行清理
        cleanup_sql = """
        WITH ranked_records AS (
            SELECT id,
                   ROW_NUMBER() OVER (
                       PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
                       ORDER BY created_at DESC, risk_score DESC
                   ) as rn
            FROM contract_risk_details
        )
        DELETE FROM contract_risk_details
        WHERE id IN (
            SELECT id FROM ranked_records WHERE rn > 1
        )
        """
        
        # 先统计要删除的记录数
        count_sql = """
        WITH ranked_records AS (
            SELECT id,
                   ROW_NUMBER() OVER (
                       PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
                       ORDER BY created_at DESC, risk_score DESC
                   ) as rn
            FROM contract_risk_details
        )
        SELECT COUNT(*) as count FROM ranked_records WHERE rn > 1
        """
        
        count_result = db_manager.execute_sql(count_sql)
        delete_count = count_result[0]['count'] if count_result else 0
        
        if delete_count == 0:
            print("✅ 没有重复数据需要清理")
            return
        
        print(f"准备删除 {delete_count} 条重复记录...")
        
        # 执行删除
        db_manager.execute_sql(cleanup_sql)
        
        print(f"✅ 成功删除 {delete_count} 条重复记录")
        
        # 验证清理结果
        check_contract_risk_duplicates()
        
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 contract_risk_details表重复数据检查工具")
    print("=" * 60)
    
    # 检查重复数据
    check_contract_risk_duplicates()
    
    # 询问是否清理
    print(f"\n" + "=" * 60)
    response = input("是否执行快速清理？(y/N): ").strip().lower()
    if response == 'y':
        quick_cleanup_duplicates()

if __name__ == "__main__":
    main()
