#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有相关表的状态
"""

import os
import duckdb

def check_all_tables():
    """检查所有表的状态"""
    # 检查两个数据库文件
    db_paths = [
        'data/risk_analysis.duckdb',
        'backend/database/trading_analysis.db'
    ]

    for db_path in db_paths:

        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            continue

        print(f"🔍 检查数据库: {db_path}")
        print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 1. 列出所有表
        print("1️⃣ 数据库中的所有表:")
        print("-" * 40)
        
        tables_sql = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        result = conn.execute(tables_sql)
        all_tables = result.fetchall()
        
        for i, (table_name,) in enumerate(all_tables, 1):
            try:
                count_sql = f"SELECT COUNT(*) FROM {table_name}"
                count = conn.execute(count_sql).fetchone()[0]
                print(f"{i:2d}. {table_name:<35} ({count:,} 条记录)")
            except Exception as e:
                print(f"{i:2d}. {table_name:<35} (查询失败: {e})")
        
        # 2. 检查目标表
        target_tables = [
            'contract_risk_details',
            'same_account_wash_trading', 
            'cross_account_wash_trading',
            'high_frequency_trading_details',
            'funding_rate_arbitrage_details'
        ]
        
        print(f"\n2️⃣ 目标表详细检查:")
        print("-" * 40)
        
        for table_name in target_tables:
            print(f"\n📋 {table_name}:")
            
            # 检查表是否存在
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            exists = conn.execute(check_sql).fetchone()
            
            if not exists:
                print(f"   ❌ 表不存在")
                continue
            
            # 获取记录数
            try:
                count_sql = f"SELECT COUNT(*) FROM {table_name}"
                count = conn.execute(count_sql).fetchone()[0]
                print(f"   📊 记录数: {count:,}")
                
                if count > 0:
                    # 显示最近几条记录的时间
                    try:
                        recent_sql = f"SELECT created_at FROM {table_name} ORDER BY created_at DESC LIMIT 3"
                        recent_result = conn.execute(recent_sql)
                        recent_times = recent_result.fetchall()
                        print(f"   🕒 最近记录时间:")
                        for i, (time_val,) in enumerate(recent_times, 1):
                            print(f"      {i}. {time_val}")
                    except Exception as e:
                        print(f"   ⚠️  无法获取时间信息: {e}")
                    
                    # 显示样本数据
                    try:
                        sample_sql = f"SELECT * FROM {table_name} LIMIT 1"
                        sample_result = conn.execute(sample_sql)
                        sample = sample_result.fetchone()
                        if sample:
                            print(f"   📝 样本数据: {str(sample)[:100]}...")
                    except Exception as e:
                        print(f"   ⚠️  无法获取样本数据: {e}")
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        # 3. 检查备份表
        print(f"\n3️⃣ 备份表:")
        print("-" * 40)
        
        backup_tables = [table for table, in all_tables if 'backup' in table[0]]
        if backup_tables:
            for table_name, in backup_tables:
                try:
                    count_sql = f"SELECT COUNT(*) FROM {table_name}"
                    count = conn.execute(count_sql).fetchone()[0]
                    print(f"   {table_name:<40} ({count:,} 条记录)")
                except:
                    print(f"   {table_name:<40} (查询失败)")
        else:
            print("   没有找到备份表")
        
        conn.close()
        print("\n" + "=" * 80 + "\n")

    except Exception as e:
        print(f"❌ 检查数据库 {db_path} 失败: {e}")
        import traceback
        traceback.print_exc()
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    check_all_tables()
