#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为现有表添加唯一性约束
由于DuckDB不支持直接ALTER TABLE ADD CONSTRAINT，需要重建表
"""

import os
import duckdb
from datetime import datetime

def add_unique_constraints():
    """为contract_risk_details表添加唯一性约束"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔧 为contract_risk_details表添加唯一性约束")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 1. 检查当前表状态
        print("1️⃣ 检查当前表状态...")
        count_sql = "SELECT COUNT(*) FROM contract_risk_details"
        total_count = conn.execute(count_sql).fetchone()[0]
        print(f"   当前记录数: {total_count:,}")
        
        # 2. 检查是否有重复数据
        print("\n2️⃣ 检查重复数据...")
        duplicate_sql = """
        SELECT COUNT(*) as duplicate_count
        FROM (
            SELECT 
                algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                COUNT(*) as count
            FROM contract_risk_details
            GROUP BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
            HAVING COUNT(*) > 1
        ) duplicates
        """
        
        duplicate_groups = conn.execute(duplicate_sql).fetchone()[0]
        print(f"   重复组数: {duplicate_groups:,}")
        
        if duplicate_groups > 0:
            print("⚠️  发现重复数据，需要先清理才能添加唯一性约束")
            response = input("是否先清理重复数据？(y/N): ").strip().lower()
            if response != 'y':
                print("❌ 用户取消操作")
                conn.close()
                return
            
            # 清理重复数据
            print("\n🧹 清理重复数据...")
            cleanup_sql = """
            DELETE FROM contract_risk_details
            WHERE id IN (
                WITH ranked_records AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
                               ORDER BY created_at DESC, risk_score DESC, id DESC
                           ) as rn
                    FROM contract_risk_details
                )
                SELECT id FROM ranked_records WHERE rn > 1
            )
            """
            
            conn.execute(cleanup_sql)
            
            # 验证清理结果
            final_count = conn.execute(count_sql).fetchone()[0]
            final_duplicates = conn.execute(duplicate_sql).fetchone()[0]
            print(f"✅ 清理完成: {total_count - final_count:,} 条重复记录已删除")
            print(f"   剩余记录数: {final_count:,}")
            print(f"   剩余重复组: {final_duplicates:,}")
            
            total_count = final_count
        
        # 3. 创建备份表
        print(f"\n3️⃣ 创建备份表...")
        backup_table = f"contract_risk_details_backup_structure_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_sql = f"CREATE TABLE {backup_table} AS SELECT * FROM contract_risk_details"
        conn.execute(backup_sql)
        print(f"✅ 备份表已创建: {backup_table}")
        
        # 4. 创建新表结构（带唯一性约束）
        print(f"\n4️⃣ 创建新表结构...")
        new_table = "contract_risk_details_new"
        
        create_new_sql = f"""
        CREATE TABLE {new_table} (
            id INTEGER PRIMARY KEY,
            algorithm_result_id INTEGER NOT NULL,
            member_id VARCHAR NOT NULL,
            contract_name VARCHAR,
            detection_type VARCHAR,
            detection_method VARCHAR,
            risk_level VARCHAR,
            risk_score DECIMAL(10,4),
            abnormal_volume DECIMAL(20,8),
            trade_count INTEGER,
            time_range VARCHAR,
            counterparty_ids VARCHAR,
            additional_data VARCHAR DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            -- 添加唯一性约束
            UNIQUE(algorithm_result_id, member_id, contract_name, detection_type, detection_method)
        )
        """
        
        conn.execute(create_new_sql)
        print(f"✅ 新表结构已创建: {new_table}")
        
        # 5. 复制数据到新表
        print(f"\n5️⃣ 复制数据到新表...")
        copy_sql = f"""
        INSERT INTO {new_table} (
            id, algorithm_result_id, member_id, contract_name, detection_type,
            detection_method, risk_level, risk_score, abnormal_volume, trade_count,
            time_range, counterparty_ids, additional_data, created_at
        )
        SELECT 
            id, algorithm_result_id, member_id, contract_name, detection_type,
            detection_method, risk_level, risk_score, abnormal_volume, trade_count,
            time_range, counterparty_ids, additional_data, created_at
        FROM contract_risk_details
        """
        
        conn.execute(copy_sql)
        
        # 验证数据复制
        new_count = conn.execute(f"SELECT COUNT(*) FROM {new_table}").fetchone()[0]
        print(f"✅ 数据复制完成: {new_count:,} 条记录")
        
        if new_count != total_count:
            print(f"⚠️  数据复制不完整: 原始{total_count:,} vs 新表{new_count:,}")
            response = input("是否继续？(y/N): ").strip().lower()
            if response != 'y':
                print("❌ 用户取消操作")
                conn.execute(f"DROP TABLE {new_table}")
                conn.close()
                return
        
        # 6. 替换原表
        print(f"\n6️⃣ 替换原表...")
        
        # 删除原表
        conn.execute("DROP TABLE contract_risk_details")
        print("✅ 原表已删除")
        
        # 重命名新表
        conn.execute(f"ALTER TABLE {new_table} RENAME TO contract_risk_details")
        print("✅ 新表已重命名为原表名")
        
        # 7. 验证唯一性约束
        print(f"\n7️⃣ 验证唯一性约束...")
        
        # 获取一条样本数据
        sample_sql = """
        SELECT algorithm_result_id, member_id, contract_name, detection_type, detection_method
        FROM contract_risk_details 
        LIMIT 1
        """
        sample = conn.execute(sample_sql).fetchone()
        
        if sample:
            print(f"使用样本数据测试: {sample}")
            
            # 尝试插入重复数据
            test_insert_sql = """
            INSERT INTO contract_risk_details (
                algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                risk_level, risk_score, abnormal_volume, trade_count, time_range, created_at
            ) VALUES (?, ?, ?, ?, ?, 'HIGH', 0.9999, 999.0, 999, 'TEST', CURRENT_TIMESTAMP)
            """
            
            try:
                conn.execute(test_insert_sql, sample)
                print("❌ 重复数据插入成功 - 唯一性约束未生效！")
            except Exception as e:
                print(f"✅ 重复数据插入失败 - 唯一性约束生效: {e}")
        
        # 8. 最终状态检查
        print(f"\n8️⃣ 最终状态检查...")
        final_count = conn.execute("SELECT COUNT(*) FROM contract_risk_details").fetchone()[0]
        final_duplicates = conn.execute(duplicate_sql.replace("duplicates", "final_duplicates")).fetchone()[0]
        
        print(f"✅ 操作完成！")
        print(f"   最终记录数: {final_count:,}")
        print(f"   重复组数: {final_duplicates:,}")
        print(f"   备份表: {backup_table}")
        
        # 显示新表结构
        print(f"\n📋 新表结构:")
        try:
            structure_sql = "SELECT sql FROM sqlite_master WHERE type='table' AND name='contract_risk_details'"
            result = conn.execute(structure_sql).fetchone()
            if result:
                print(result[0])
        except:
            print("无法获取表结构")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 数据库表结构升级工具")
    print("为contract_risk_details表添加唯一性约束")
    print("=" * 80)
    
    print("⚠️  注意事项:")
    print("1. 此操作会重建表结构")
    print("2. 会自动创建备份表")
    print("3. 如有重复数据会先清理")
    print("4. 操作不可逆，请确保已备份重要数据")
    
    response = input("\n是否继续？(y/N): ").strip().lower()
    if response == 'y':
        add_unique_constraints()
    else:
        print("❌ 用户取消操作")

if __name__ == "__main__":
    main()
