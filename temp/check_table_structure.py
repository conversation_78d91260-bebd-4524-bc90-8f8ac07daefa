#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构，特别是唯一性约束
"""

import os
import duckdb

def check_table_structure():
    """检查数据库表结构"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 检查数据库表结构")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 检查contract_risk_details表结构
        table_name = 'contract_risk_details'
        print(f"\n📋 {table_name} 表结构:")
        print("-" * 60)
        
        # 获取表结构
        try:
            pragma_sql = f"PRAGMA table_info({table_name})"
            result = conn.execute(pragma_sql)
            columns = result.fetchall()
            
            print("列信息:")
            print(f"{'序号':<4} {'列名':<25} {'类型':<15} {'非空':<6} {'默认值':<15} {'主键'}")
            print("-" * 80)
            for col in columns:
                cid, name, type_name, notnull, dflt_value, pk = col
                print(f"{cid:<4} {name:<25} {type_name:<15} {'是' if notnull else '否':<6} {str(dflt_value or ''):<15} {'是' if pk else '否'}")
        except Exception as e:
            print(f"获取列信息失败: {e}")
        
        # 获取索引信息
        try:
            print(f"\n索引信息:")
            print("-" * 40)
            index_sql = f"PRAGMA index_list({table_name})"
            result = conn.execute(index_sql)
            indexes = result.fetchall()
            
            if indexes:
                for idx in indexes:
                    seq, name, unique, origin, partial = idx
                    print(f"索引名: {name}, 唯一: {'是' if unique else '否'}, 来源: {origin}")
                    
                    # 获取索引详细信息
                    try:
                        detail_sql = f"PRAGMA index_info({name})"
                        detail_result = conn.execute(detail_sql)
                        details = detail_result.fetchall()
                        for detail in details:
                            seqno, cid, col_name = detail
                            print(f"  - 列: {col_name}")
                    except:
                        pass
            else:
                print("没有索引")
        except Exception as e:
            print(f"获取索引信息失败: {e}")
        
        # 检查约束信息（如果支持）
        try:
            print(f"\n约束信息:")
            print("-" * 40)
            # DuckDB可能不支持这个查询，但试试看
            constraint_sql = f"""
            SELECT sql FROM sqlite_master 
            WHERE type='table' AND name='{table_name}'
            """
            result = conn.execute(constraint_sql)
            create_sql = result.fetchone()
            if create_sql:
                print("建表语句:")
                print(create_sql[0])
            else:
                print("无法获取建表语句")
        except Exception as e:
            print(f"获取约束信息失败: {e}")
        
        # 测试插入重复数据
        print(f"\n🧪 测试唯一性约束:")
        print("-" * 40)
        
        try:
            # 先查看现有数据
            sample_sql = f"""
            SELECT algorithm_result_id, member_id, contract_name, detection_type, detection_method
            FROM {table_name} 
            LIMIT 1
            """
            result = conn.execute(sample_sql)
            sample = result.fetchone()
            
            if sample:
                print(f"找到样本数据: {sample}")
                
                # 尝试插入重复数据
                insert_sql = f"""
                INSERT INTO {table_name} (
                    algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                    risk_level, risk_score, abnormal_volume, trade_count, time_range, created_at
                ) VALUES (?, ?, ?, ?, ?, 'HIGH', 0.5, 100.0, 10, '2025-08-05', CURRENT_TIMESTAMP)
                """
                
                try:
                    conn.execute(insert_sql, sample)
                    print("⚠️  重复数据插入成功 - 说明没有唯一性约束！")
                    
                    # 删除刚插入的测试数据
                    delete_sql = f"""
                    DELETE FROM {table_name} 
                    WHERE algorithm_result_id = ? AND member_id = ? AND contract_name = ? 
                    AND detection_type = ? AND detection_method = ? AND risk_score = 0.5
                    """
                    conn.execute(delete_sql, sample)
                    print("✅ 测试数据已删除")
                    
                except Exception as insert_error:
                    print(f"✅ 重复数据插入失败 - 唯一性约束生效: {insert_error}")
            else:
                print("没有找到样本数据进行测试")
                
        except Exception as e:
            print(f"测试失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
