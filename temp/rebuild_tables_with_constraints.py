#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重建表结构，正确添加唯一性约束和自增主键
"""

import os
import duckdb
from datetime import datetime

def rebuild_tables_with_constraints():
    """重建表结构，添加唯一性约束"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔧 重建表结构，添加唯一性约束")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    # 定义新的表结构（带唯一性约束）
    table_definitions = {
        'contract_risk_details': {
            'create_sql': """
            CREATE TABLE contract_risk_details_new (
                id INTEGER PRIMARY KEY,
                algorithm_result_id INTEGER NOT NULL,
                member_id VARCHAR NOT NULL,
                contract_name VARCHAR,
                detection_type VARCHAR,
                detection_method VARCHAR,
                risk_level VARCHAR,
                risk_score DECIMAL(10,4),
                abnormal_volume DECIMAL(20,8),
                trade_count INTEGER,
                time_range VARCHAR,
                counterparty_ids VARCHAR,
                additional_data VARCHAR DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(algorithm_result_id, member_id, contract_name, detection_type, detection_method)
            )
            """,
            'unique_fields': ['algorithm_result_id', 'member_id', 'contract_name', 'detection_type', 'detection_method']
        },
        
        'same_account_wash_trading': {
            'create_sql': """
            CREATE TABLE same_account_wash_trading_new (
                id INTEGER PRIMARY KEY,
                wash_trading_id INTEGER NOT NULL,
                user_id VARCHAR NOT NULL,
                contract_name VARCHAR NOT NULL,
                long_position_id VARCHAR,
                short_position_id VARCHAR,
                long_open_time TIMESTAMP,
                short_open_time TIMESTAMP,
                long_volume DECIMAL(20,8),
                short_volume DECIMAL(20,8),
                long_price DECIMAL(20,8),
                short_price DECIMAL(20,8),
                time_gap INTEGER,
                volume_ratio DECIMAL(5,4),
                price_correlation DECIMAL(5,4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, contract_name, long_position_id, short_position_id)
            )
            """,
            'unique_fields': ['user_id', 'contract_name', 'long_position_id', 'short_position_id']
        },
        
        'cross_account_wash_trading': {
            'create_sql': """
            CREATE TABLE cross_account_wash_trading_new (
                id INTEGER PRIMARY KEY,
                wash_trading_id INTEGER NOT NULL,
                user_a_id VARCHAR NOT NULL,
                user_b_id VARCHAR NOT NULL,
                contract_name VARCHAR NOT NULL,
                bd_relationship VARCHAR,
                trade_a_id VARCHAR,
                trade_b_id VARCHAR,
                trade_a_side INTEGER,
                trade_b_side INTEGER,
                trade_a_time TIMESTAMP,
                trade_b_time TIMESTAMP,
                trade_a_volume DECIMAL(20,8),
                trade_b_volume DECIMAL(20,8),
                trade_a_price DECIMAL(20,8),
                trade_b_price DECIMAL(20,8),
                time_correlation DECIMAL(5,4),
                volume_correlation DECIMAL(5,4),
                price_correlation DECIMAL(5,4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                pair_index INTEGER DEFAULT 1,
                trade_a_open_time TIMESTAMP,
                trade_a_close_time TIMESTAMP,
                trade_a_profit DECIMAL(20,8),
                trade_b_open_time TIMESTAMP,
                trade_b_close_time TIMESTAMP,
                trade_b_profit DECIMAL(20,8),
                open_time_diff_seconds INTEGER DEFAULT 0,
                close_time_diff_seconds INTEGER DEFAULT 0,
                total_amount DECIMAL(20,8),
                net_profit DECIMAL(20,8),
                profit_hedge_score DECIMAL(10,6) DEFAULT 0,
                wash_score DECIMAL(10,6) DEFAULT 0,
                trade_pair_detail_json VARCHAR,
                UNIQUE(user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id)
            )
            """,
            'unique_fields': ['user_a_id', 'user_b_id', 'contract_name', 'trade_a_id', 'trade_b_id']
        },
        
        'high_frequency_trading_details': {
            'create_sql': """
            CREATE TABLE high_frequency_trading_details_new (
                id INTEGER PRIMARY KEY,
                algorithm_result_id INTEGER NOT NULL,
                user_id VARCHAR NOT NULL,
                contract_name VARCHAR NOT NULL,
                trade_count INTEGER,
                avg_holding_time INTEGER,
                max_frequency DECIMAL(10,4),
                volume_concentration DECIMAL(5,4),
                time_pattern VARCHAR,
                risk_indicators JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(algorithm_result_id, user_id, contract_name)
            )
            """,
            'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name']
        },
        
        'funding_rate_arbitrage_details': {
            'create_sql': """
            CREATE TABLE funding_rate_arbitrage_details_new (
                id INTEGER PRIMARY KEY,
                algorithm_result_id INTEGER NOT NULL,
                user_id VARCHAR NOT NULL,
                contract_name VARCHAR NOT NULL,
                funding_rate DECIMAL(10,8),
                position_size DECIMAL(20,8),
                holding_duration INTEGER,
                estimated_profit DECIMAL(20,8),
                risk_exposure DECIMAL(5,4),
                market_conditions JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(algorithm_result_id, user_id, contract_name)
            )
            """,
            'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name']
        }
    }
    
    try:
        conn = duckdb.connect(db_path)
        
        for table_name, config in table_definitions.items():
            print(f"\n🔧 处理表: {table_name}")
            print("-" * 50)
            
            create_sql = config['create_sql']
            unique_fields = config['unique_fields']
            new_table = f"{table_name}_new"
            
            # 1. 检查原表是否存在
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            exists = conn.execute(check_sql).fetchone()
            
            if not exists:
                print(f"⚠️  原表不存在，跳过")
                continue
            
            # 2. 获取原表数据量
            count_sql = f"SELECT COUNT(*) FROM {table_name}"
            original_count = conn.execute(count_sql).fetchone()[0]
            print(f"原表记录数: {original_count:,}")
            
            # 3. 创建备份表
            backup_table = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if original_count > 0:
                backup_sql = f"CREATE TABLE {backup_table} AS SELECT * FROM {table_name}"
                conn.execute(backup_sql)
                print(f"📦 备份表已创建: {backup_table}")
            
            # 4. 创建新表
            print("🏗️  创建新表结构...")
            conn.execute(create_sql)
            
            # 5. 复制数据（如果有）
            if original_count > 0:
                print("📋 复制数据...")
                
                # 获取原表列名
                pragma_sql = f"PRAGMA table_info({table_name})"
                columns_info = conn.execute(pragma_sql).fetchall()
                column_names = [col[1] for col in columns_info]  # 包含所有列

                # 复制数据，包括原始id
                copy_sql = f"""
                INSERT INTO {new_table} ({', '.join(column_names)})
                SELECT {', '.join(column_names)}
                FROM {table_name}
                """
                
                conn.execute(copy_sql)
                
                # 验证数据复制
                new_count = conn.execute(f"SELECT COUNT(*) FROM {new_table}").fetchone()[0]
                print(f"新表记录数: {new_count:,}")
                
                if new_count != original_count:
                    print(f"⚠️  数据复制不完整: 原始{original_count:,} vs 新表{new_count:,}")
            
            # 6. 替换原表
            print("🔄 替换原表...")
            conn.execute(f"DROP TABLE {table_name}")
            conn.execute(f"ALTER TABLE {new_table} RENAME TO {table_name}")
            
            # 7. 验证唯一性约束
            print("🧪 验证唯一性约束...")
            
            # 构建测试数据
            if table_name == 'contract_risk_details':
                test_data = (999, 1, 'test_user', 'BTC_USDT', 'wash_trading', 'cross_account')
                test_sql = """
                INSERT INTO contract_risk_details (
                    id, algorithm_result_id, member_id, contract_name, detection_type, detection_method
                ) VALUES (?, ?, ?, ?, ?, ?)
                """
            elif table_name == 'same_account_wash_trading':
                test_data = (999, 1, 'test_user', 'BTC_USDT', 'long_123', 'short_456')
                test_sql = """
                INSERT INTO same_account_wash_trading (
                    id, wash_trading_id, user_id, contract_name, long_position_id, short_position_id
                ) VALUES (?, ?, ?, ?, ?, ?)
                """
            elif table_name == 'cross_account_wash_trading':
                test_data = (999, 1, 'user_a', 'user_b', 'BTC_USDT', 'trade_123', 'trade_456')
                test_sql = """
                INSERT INTO cross_account_wash_trading (
                    id, wash_trading_id, user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """
            elif table_name == 'high_frequency_trading_details':
                test_data = (999, 1, 'test_user', 'BTC_USDT')
                test_sql = """
                INSERT INTO high_frequency_trading_details (
                    id, algorithm_result_id, user_id, contract_name
                ) VALUES (?, ?, ?, ?)
                """
            elif table_name == 'funding_rate_arbitrage_details':
                test_data = (999, 1, 'test_user', 'BTC_USDT')
                test_sql = """
                INSERT INTO funding_rate_arbitrage_details (
                    id, algorithm_result_id, user_id, contract_name
                ) VALUES (?, ?, ?, ?)
                """
            
            try:
                # 第一次插入
                conn.execute(test_sql, test_data)
                print("✅ 第一次插入成功")
                
                # 第二次插入相同数据
                try:
                    conn.execute(test_sql, test_data)
                    print("❌ 第二次插入成功 - 唯一性约束未生效！")
                except Exception as e:
                    print(f"✅ 第二次插入失败 - 唯一性约束生效")
                
                # 清理测试数据
                placeholders = ', '.join(['?' for _ in unique_fields])
                delete_sql = f"DELETE FROM {table_name} WHERE ({', '.join(unique_fields)}) = ({placeholders})"
                conn.execute(delete_sql, test_data)
                
            except Exception as e:
                print(f"⚠️  测试插入失败: {e}")
            
            print(f"✅ 表 {table_name} 重建完成")
        
        conn.close()
        
        print(f"\n" + "=" * 80)
        print("🎉 所有表重建完成！")
        print("💡 现在所有表都有：")
        print("   - 自增主键 (AUTOINCREMENT)")
        print("   - 唯一性约束 (UNIQUE)")
        print("   - 防重复数据机制")
        
    except Exception as e:
        print(f"❌ 重建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    rebuild_tables_with_constraints()
