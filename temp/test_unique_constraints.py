#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试唯一性约束是否生效
"""

import os
import duckdb

def test_unique_constraints():
    """测试唯一性约束"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🧪 测试唯一性约束")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 测试contract_risk_details表
        print(f"\n📋 测试 contract_risk_details:")
        print("-" * 50)
        
        try:
            # 第一次插入
            conn.execute("""
                INSERT INTO contract_risk_details (
                    id, algorithm_result_id, member_id, contract_name, detection_type, detection_method
                ) VALUES (999, 1, 'test_user', 'BTC_USDT', 'wash_trading', 'cross_account')
            """)
            print("✅ 第一次插入成功")
            
            # 第二次插入相同数据
            try:
                conn.execute("""
                    INSERT INTO contract_risk_details (
                        id, algorithm_result_id, member_id, contract_name, detection_type, detection_method
                    ) VALUES (1000, 1, 'test_user', 'BTC_USDT', 'wash_trading', 'cross_account')
                """)
                print("❌ 第二次插入成功 - 唯一性约束未生效！")
            except Exception as e:
                print(f"✅ 第二次插入失败 - 唯一性约束生效: {str(e)[:100]}...")
            
            # 清理测试数据
            conn.execute("DELETE FROM contract_risk_details WHERE id IN (999, 1000)")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        # 测试same_account_wash_trading表
        print(f"\n📋 测试 same_account_wash_trading:")
        print("-" * 50)
        
        try:
            # 第一次插入
            conn.execute("""
                INSERT INTO same_account_wash_trading (
                    id, wash_trading_id, user_id, contract_name, long_position_id, short_position_id
                ) VALUES (999, 1, 'test_user', 'BTC_USDT', 'long_123', 'short_456')
            """)
            print("✅ 第一次插入成功")
            
            # 第二次插入相同数据
            try:
                conn.execute("""
                    INSERT INTO same_account_wash_trading (
                        id, wash_trading_id, user_id, contract_name, long_position_id, short_position_id
                    ) VALUES (1000, 2, 'test_user', 'BTC_USDT', 'long_123', 'short_456')
                """)
                print("❌ 第二次插入成功 - 唯一性约束未生效！")
            except Exception as e:
                print(f"✅ 第二次插入失败 - 唯一性约束生效: {str(e)[:100]}...")
            
            # 清理测试数据
            conn.execute("DELETE FROM same_account_wash_trading WHERE id IN (999, 1000)")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        # 检查表结构
        print(f"\n📋 检查表结构:")
        print("-" * 50)
        
        for table_name in ['contract_risk_details', 'same_account_wash_trading']:
            try:
                create_sql = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                result = conn.execute(create_sql).fetchone()
                if result:
                    sql = result[0]
                    if 'UNIQUE' in sql:
                        print(f"✅ {table_name}: 包含UNIQUE约束")
                    else:
                        print(f"❌ {table_name}: 不包含UNIQUE约束")
                else:
                    print(f"⚠️  {table_name}: 无法获取建表语句")
            except Exception as e:
                print(f"❌ {table_name}: 检查失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_unique_constraints()
