#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查表的详细结构
"""

import os
import duckdb

def check_table_schemas():
    """检查表的详细结构"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 检查表结构")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    target_tables = [
        'contract_risk_details',
        'same_account_wash_trading', 
        'cross_account_wash_trading',
        'high_frequency_trading_details',
        'funding_rate_arbitrage_details'
    ]
    
    try:
        conn = duckdb.connect(db_path)
        
        for table_name in target_tables:
            print(f"\n📋 {table_name}:")
            print("-" * 60)
            
            # 检查表是否存在
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            exists = conn.execute(check_sql).fetchone()
            
            if not exists:
                print(f"❌ 表不存在")
                continue
            
            # 获取建表语句
            try:
                create_sql = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                result = conn.execute(create_sql).fetchone()
                if result:
                    print("建表语句:")
                    print(result[0])
                else:
                    print("无法获取建表语句")
            except Exception as e:
                print(f"获取建表语句失败: {e}")
            
            # 获取表结构
            try:
                pragma_sql = f"PRAGMA table_info({table_name})"
                result = conn.execute(pragma_sql)
                columns = result.fetchall()
                
                print(f"\n列信息:")
                print(f"{'序号':<4} {'列名':<25} {'类型':<15} {'非空':<6} {'默认值':<15} {'主键'}")
                print("-" * 80)
                for col in columns:
                    cid, name, type_name, notnull, dflt_value, pk = col
                    print(f"{cid:<4} {name:<25} {type_name:<15} {'是' if notnull else '否':<6} {str(dflt_value or ''):<15} {'是' if pk else '否'}")
            except Exception as e:
                print(f"获取列信息失败: {e}")
            
            # 测试简单插入
            print(f"\n🧪 测试插入:")
            try:
                # 尝试插入一条简单记录
                if table_name == 'contract_risk_details':
                    test_sql = """
                    INSERT INTO contract_risk_details (
                        algorithm_result_id, member_id, contract_name, detection_type, detection_method
                    ) VALUES (999, 'test_user_999', 'TEST_CONTRACT', 'test_type', 'test_method')
                    """
                elif table_name == 'same_account_wash_trading':
                    test_sql = """
                    INSERT INTO same_account_wash_trading (
                        wash_trading_id, user_id, contract_name, long_position_id, short_position_id
                    ) VALUES (999, 'test_user_999', 'TEST_CONTRACT', 'long_999', 'short_999')
                    """
                elif table_name == 'cross_account_wash_trading':
                    test_sql = """
                    INSERT INTO cross_account_wash_trading (
                        wash_trading_id, user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id
                    ) VALUES (999, 'user_a_999', 'user_b_999', 'TEST_CONTRACT', 'trade_a_999', 'trade_b_999')
                    """
                elif table_name == 'high_frequency_trading_details':
                    test_sql = """
                    INSERT INTO high_frequency_trading_details (
                        algorithm_result_id, user_id, contract_name, trade_count
                    ) VALUES (999, 'test_user_999', 'TEST_CONTRACT', 100)
                    """
                elif table_name == 'funding_rate_arbitrage_details':
                    test_sql = """
                    INSERT INTO funding_rate_arbitrage_details (
                        algorithm_result_id, user_id, contract_name, funding_rate
                    ) VALUES (999, 'test_user_999', 'TEST_CONTRACT', 0.01)
                    """
                
                conn.execute(test_sql)
                print("✅ 第一次插入成功")
                
                # 尝试插入相同数据
                try:
                    conn.execute(test_sql)
                    print("❌ 第二次插入成功 - 唯一性约束未生效！")
                except Exception as e:
                    print(f"✅ 第二次插入失败 - 唯一性约束生效: {str(e)[:100]}...")
                
                # 清理测试数据
                cleanup_sql = f"DELETE FROM {table_name} WHERE algorithm_result_id = 999 OR wash_trading_id = 999"
                conn.execute(cleanup_sql)
                print("🧹 测试数据已清理")
                
            except Exception as e:
                print(f"❌ 插入测试失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_schemas()
