#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：添加唯一性约束
用于为合约风险分析相关表添加唯一性约束，防止重复数据

功能：
1. 检查现有表结构
2. 添加唯一性约束
3. 处理约束冲突
4. 生成迁移报告

作者：系统管理员
创建时间：2025-08-05
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database.duckdb_manager import db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('temp/migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigration:
    """数据库迁移工具"""
    
    def __init__(self):
        self.db_manager = db_manager
        self.migration_config = {
            'contract_risk_details': {
                'constraint_name': 'unique_contract_risk',
                'unique_fields': ['algorithm_result_id', 'member_id', 'contract_name', 'detection_type', 'detection_method'],
                'backup_table': 'contract_risk_details_backup'
            },
            'same_account_wash_trading': {
                'constraint_name': 'unique_same_account_wash',
                'unique_fields': ['user_id', 'contract_name', 'long_position_id', 'short_position_id'],
                'backup_table': 'same_account_wash_trading_backup'
            },
            'cross_account_wash_trading': {
                'constraint_name': 'unique_cross_account_wash',
                'unique_fields': ['user_a_id', 'user_b_id', 'contract_name', 'trade_a_id', 'trade_b_id'],
                'backup_table': 'cross_account_wash_trading_backup'
            },
            'high_frequency_trading_details': {
                'constraint_name': 'unique_hft_details',
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'backup_table': 'high_frequency_trading_details_backup'
            },
            'funding_rate_arbitrage_details': {
                'constraint_name': 'unique_funding_arbitrage',
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'backup_table': 'funding_rate_arbitrage_details_backup'
            }
        }
    
    def check_current_schema(self) -> Dict[str, Any]:
        """检查当前数据库结构"""
        logger.info("🔍 检查当前数据库结构...")
        
        schema_info = {
            'timestamp': datetime.now().isoformat(),
            'tables': {}
        }
        
        for table_name in self.migration_config.keys():
            table_info = self._get_table_info(table_name)
            schema_info['tables'][table_name] = table_info
        
        return schema_info
    
    def _get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            # 检查表是否存在
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            result = self.db_manager.execute_sql(check_sql)
            
            if not result:
                return {'exists': False, 'error': '表不存在'}
            
            # 获取表结构
            pragma_sql = f"PRAGMA table_info({table_name})"
            columns = self.db_manager.execute_sql(pragma_sql)
            
            # 获取索引信息
            index_sql = f"PRAGMA index_list({table_name})"
            indexes = self.db_manager.execute_sql(index_sql)
            
            # 获取记录数
            count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
            count_result = self.db_manager.execute_sql(count_sql)
            record_count = count_result[0]['count'] if count_result else 0
            
            return {
                'exists': True,
                'columns': columns,
                'indexes': indexes,
                'record_count': record_count
            }
            
        except Exception as e:
            logger.error(f"获取表 {table_name} 信息失败: {e}")
            return {'exists': False, 'error': str(e)}
    
    def migrate_with_constraints(self, dry_run: bool = True) -> Dict[str, Any]:
        """执行迁移，添加唯一性约束"""
        logger.info(f"🚀 开始数据库迁移 (dry_run={dry_run})...")
        
        migration_result = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'tables': {},
            'summary': {
                'total_tables': len(self.migration_config),
                'successful_migrations': 0,
                'failed_migrations': 0,
                'errors': []
            }
        }
        
        for table_name, config in self.migration_config.items():
            logger.info(f"迁移表: {table_name}")
            table_result = self._migrate_table(table_name, config, dry_run)
            migration_result['tables'][table_name] = table_result
            
            if table_result.get('success', False):
                migration_result['summary']['successful_migrations'] += 1
            else:
                migration_result['summary']['failed_migrations'] += 1
                if 'error' in table_result:
                    migration_result['summary']['errors'].append(f"{table_name}: {table_result['error']}")
        
        # 保存迁移报告
        report_path = f"temp/migration_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(migration_result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 迁移完成，报告已保存到: {report_path}")
        return migration_result
    
    def _migrate_table(self, table_name: str, config: Dict, dry_run: bool) -> Dict[str, Any]:
        """迁移单个表"""
        try:
            # 检查表是否存在
            table_info = self._get_table_info(table_name)
            if not table_info['exists']:
                return {'success': False, 'error': '表不存在'}
            
            unique_fields = config['unique_fields']
            backup_table = config['backup_table']
            
            if dry_run:
                # 检查是否有重复数据会导致约束冲突
                conflict_check = self._check_constraint_conflicts(table_name, unique_fields)
                return {
                    'success': True,
                    'dry_run': True,
                    'record_count': table_info['record_count'],
                    'potential_conflicts': conflict_check['conflict_count'],
                    'message': f'表 {table_name} 准备就绪，潜在冲突: {conflict_check["conflict_count"]} 条'
                }
            
            # 实际迁移过程
            logger.info(f"开始迁移表 {table_name}...")
            
            # 1. 创建备份表
            self._create_backup_table(table_name, backup_table)
            
            # 2. 检查并处理重复数据
            conflicts = self._check_constraint_conflicts(table_name, unique_fields)
            if conflicts['conflict_count'] > 0:
                logger.warning(f"表 {table_name} 存在 {conflicts['conflict_count']} 条重复数据，需要先去重")
                # 这里可以调用去重工具
                return {
                    'success': False,
                    'error': f'存在 {conflicts["conflict_count"]} 条重复数据，请先执行去重操作'
                }
            
            # 3. 重建表结构（SQLite不支持直接添加唯一约束）
            success = self._rebuild_table_with_constraints(table_name, config)
            
            if success:
                logger.info(f"✅ 表 {table_name} 迁移成功")
                return {
                    'success': True,
                    'record_count': table_info['record_count'],
                    'backup_table': backup_table,
                    'message': f'表 {table_name} 迁移成功，已添加唯一性约束'
                }
            else:
                return {'success': False, 'error': '重建表失败'}
            
        except Exception as e:
            logger.error(f"迁移表 {table_name} 失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _check_constraint_conflicts(self, table_name: str, unique_fields: List[str]) -> Dict[str, Any]:
        """检查唯一性约束冲突"""
        try:
            unique_fields_str = ', '.join(unique_fields)
            
            conflict_sql = f"""
            SELECT {unique_fields_str}, COUNT(*) as count
            FROM {table_name}
            GROUP BY {unique_fields_str}
            HAVING COUNT(*) > 1
            """
            
            conflicts = self.db_manager.execute_sql(conflict_sql)
            conflict_count = sum(conflict['count'] - 1 for conflict in conflicts)
            
            return {
                'conflict_count': conflict_count,
                'conflict_groups': len(conflicts),
                'sample_conflicts': conflicts[:5]
            }
            
        except Exception as e:
            logger.error(f"检查约束冲突失败: {e}")
            return {'conflict_count': 0, 'error': str(e)}
    
    def _create_backup_table(self, original_table: str, backup_table: str):
        """创建备份表"""
        try:
            # 删除已存在的备份表
            drop_sql = f"DROP TABLE IF EXISTS {backup_table}"
            self.db_manager.execute_sql(drop_sql)
            
            # 创建备份表
            backup_sql = f"CREATE TABLE {backup_table} AS SELECT * FROM {original_table}"
            self.db_manager.execute_sql(backup_sql)
            
            logger.info(f"✅ 创建备份表 {backup_table}")
            
        except Exception as e:
            logger.error(f"创建备份表失败: {e}")
            raise
    
    def _rebuild_table_with_constraints(self, table_name: str, config: Dict) -> bool:
        """重建表并添加唯一性约束"""
        try:
            unique_fields = config['unique_fields']
            unique_constraint = f"UNIQUE({', '.join(unique_fields)})"
            
            # 获取原表结构
            pragma_sql = f"PRAGMA table_info({table_name})"
            columns = self.db_manager.execute_sql(pragma_sql)
            
            # 构建新表的CREATE语句
            column_defs = []
            for col in columns:
                col_def = f"{col['name']} {col['type']}"
                if col['notnull']:
                    col_def += " NOT NULL"
                if col['dflt_value']:
                    col_def += f" DEFAULT {col['dflt_value']}"
                if col['pk']:
                    col_def += " PRIMARY KEY"
                column_defs.append(col_def)
            
            # 添加唯一性约束
            column_defs.append(unique_constraint)
            
            # 创建新表
            temp_table = f"{table_name}_new"
            create_sql = f"""
            CREATE TABLE {temp_table} (
                {', '.join(column_defs)}
            )
            """
            
            self.db_manager.execute_sql(create_sql)
            
            # 复制数据
            copy_sql = f"INSERT INTO {temp_table} SELECT * FROM {table_name}"
            self.db_manager.execute_sql(copy_sql)
            
            # 替换原表
            self.db_manager.execute_sql(f"DROP TABLE {table_name}")
            self.db_manager.execute_sql(f"ALTER TABLE {temp_table} RENAME TO {table_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"重建表 {table_name} 失败: {e}")
            return False

def main():
    """主函数"""
    print("🔧 数据库迁移工具 - 添加唯一性约束")
    print("=" * 50)
    
    migration = DatabaseMigration()
    
    # 1. 检查当前结构
    print("\n1️⃣ 检查当前数据库结构...")
    schema = migration.check_current_schema()
    
    print(f"\n📊 数据库状态:")
    for table_name, table_info in schema['tables'].items():
        if table_info['exists']:
            print(f"   {table_name}: {table_info['record_count']} 条记录")
        else:
            print(f"   {table_name}: 不存在")
    
    # 2. 执行迁移预演
    print(f"\n2️⃣ 迁移预演...")
    dry_result = migration.migrate_with_constraints(dry_run=True)
    
    print(f"\n📋 预演结果:")
    for table_name, table_result in dry_result['tables'].items():
        if table_result.get('success'):
            conflicts = table_result.get('potential_conflicts', 0)
            if conflicts > 0:
                print(f"   {table_name}: ⚠️  有 {conflicts} 条潜在冲突")
            else:
                print(f"   {table_name}: ✅ 准备就绪")
        else:
            print(f"   {table_name}: ❌ {table_result.get('error', '未知错误')}")
    
    # 3. 询问是否执行实际迁移
    if dry_result['summary']['failed_migrations'] == 0:
        print(f"\n3️⃣ 执行迁移...")
        response = input("是否执行实际迁移？(y/N): ").strip().lower()
        
        if response == 'y':
            print("执行实际迁移...")
            result = migration.migrate_with_constraints(dry_run=False)
            
            if result['summary']['failed_migrations'] == 0:
                print(f"✅ 迁移完成！成功迁移 {result['summary']['successful_migrations']} 个表")
            else:
                print(f"⚠️  迁移部分完成：成功 {result['summary']['successful_migrations']} 个，失败 {result['summary']['failed_migrations']} 个")
                for error in result['summary']['errors']:
                    print(f"   错误: {error}")
        else:
            print("❌ 用户取消了迁移操作")
    else:
        print(f"\n❌ 预演发现问题，请先解决以下问题后再执行迁移:")
        for error in dry_result['summary']['errors']:
            print(f"   - {error}")

if __name__ == "__main__":
    main()
