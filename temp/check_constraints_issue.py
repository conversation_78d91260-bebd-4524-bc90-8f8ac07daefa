#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查约束冲突问题
"""

import os
import duckdb

def check_constraints_issue():
    """检查约束冲突问题"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 检查约束冲突问题")
    print(f"数据库: {db_path}")
    print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 检查cross_account_wash_trading表的约束
        table_name = 'cross_account_wash_trading'
        print(f"\n📋 检查 {table_name} 表:")
        print("-" * 60)
        
        # 获取建表语句
        create_sql = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'"
        result = conn.execute(create_sql).fetchone()
        if result:
            sql = result[0]
            print("建表语句:")
            print(sql)
            
            # 分析约束
            print(f"\n约束分析:")
            if 'PRIMARY KEY' in sql:
                print("✅ 包含PRIMARY KEY约束")
            if 'UNIQUE' in sql:
                unique_count = sql.count('UNIQUE')
                print(f"✅ 包含 {unique_count} 个UNIQUE约束")
                
                # 提取UNIQUE约束内容
                import re
                unique_matches = re.findall(r'UNIQUE\([^)]+\)', sql)
                for i, match in enumerate(unique_matches, 1):
                    print(f"   UNIQUE约束 {i}: {match}")
        
        # 测试INSERT OR REPLACE
        print(f"\n🧪 测试INSERT OR REPLACE:")
        print("-" * 40)
        
        try:
            test_sql = """
            INSERT OR REPLACE INTO cross_account_wash_trading (
                id, wash_trading_id, user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id
            ) VALUES (9999, 1, 'test_a', 'test_b', 'BTC_USDT', 'trade_123', 'trade_456')
            """
            conn.execute(test_sql)
            print("✅ INSERT OR REPLACE 成功")
            
            # 清理测试数据
            conn.execute("DELETE FROM cross_account_wash_trading WHERE id = 9999")
            
        except Exception as e:
            print(f"❌ INSERT OR REPLACE 失败: {e}")
            
            # 尝试使用INSERT OR IGNORE
            print(f"\n🧪 测试INSERT OR IGNORE:")
            try:
                test_sql = """
                INSERT OR IGNORE INTO cross_account_wash_trading (
                    id, wash_trading_id, user_a_id, user_b_id, contract_name, trade_a_id, trade_b_id
                ) VALUES (9999, 1, 'test_a', 'test_b', 'BTC_USDT', 'trade_123', 'trade_456')
                """
                conn.execute(test_sql)
                print("✅ INSERT OR IGNORE 成功")
                
                # 再次插入相同数据
                conn.execute(test_sql)
                print("✅ 第二次INSERT OR IGNORE 成功（忽略重复）")
                
                # 清理测试数据
                conn.execute("DELETE FROM cross_account_wash_trading WHERE id = 9999")
                
            except Exception as e2:
                print(f"❌ INSERT OR IGNORE 也失败: {e2}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_constraints_issue()
