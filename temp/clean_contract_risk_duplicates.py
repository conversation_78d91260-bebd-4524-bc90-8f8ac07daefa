#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理contract_risk_details表的重复数据
保留最新的记录（created_at最大的）
"""

import os
import duckdb
from datetime import datetime

def clean_contract_risk_duplicates():
    """清理contract_risk_details表的重复数据"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🧹 清理contract_risk_details表的重复数据")
    print(f"数据库: {db_path}")
    print("=" * 60)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 1. 检查当前状态
        print("1️⃣ 检查当前数据状态...")
        
        total_sql = "SELECT COUNT(*) FROM contract_risk_details"
        total_count = conn.execute(total_sql).fetchone()[0]
        print(f"   总记录数: {total_count:,}")
        
        # 检查重复数据
        duplicate_sql = """
        SELECT COUNT(*) as duplicate_count
        FROM (
            SELECT 
                algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                COUNT(*) as count
            FROM contract_risk_details
            GROUP BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
            HAVING COUNT(*) > 1
        ) duplicates
        """
        
        duplicate_groups = conn.execute(duplicate_sql).fetchone()[0]
        print(f"   重复组数: {duplicate_groups:,}")
        
        # 计算要删除的记录数
        delete_count_sql = """
        WITH ranked_records AS (
            SELECT id,
                   ROW_NUMBER() OVER (
                       PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
                       ORDER BY created_at DESC, risk_score DESC, id DESC
                   ) as rn
            FROM contract_risk_details
        )
        SELECT COUNT(*) FROM ranked_records WHERE rn > 1
        """
        
        delete_count = conn.execute(delete_count_sql).fetchone()[0]
        print(f"   将删除记录数: {delete_count:,}")
        print(f"   清理后记录数: {total_count - delete_count:,}")
        
        if delete_count == 0:
            print("✅ 没有重复数据需要清理")
            conn.close()
            return
        
        # 2. 确认操作
        print(f"\n2️⃣ 确认清理操作...")
        print(f"⚠️  即将删除 {delete_count:,} 条重复记录")
        print(f"   保留策略: 保留最新的记录（created_at最大，risk_score最高）")
        
        response = input("是否继续？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 用户取消操作")
            conn.close()
            return
        
        # 3. 创建备份（可选）
        print(f"\n3️⃣ 创建备份...")
        backup_table = f"contract_risk_details_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_sql = f"CREATE TABLE {backup_table} AS SELECT * FROM contract_risk_details"
        conn.execute(backup_sql)
        print(f"✅ 备份表已创建: {backup_table}")
        
        # 4. 执行清理
        print(f"\n4️⃣ 执行清理...")
        
        # 使用CTE删除重复记录
        cleanup_sql = """
        DELETE FROM contract_risk_details
        WHERE id IN (
            WITH ranked_records AS (
                SELECT id,
                       ROW_NUMBER() OVER (
                           PARTITION BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
                           ORDER BY created_at DESC, risk_score DESC, id DESC
                       ) as rn
                FROM contract_risk_details
            )
            SELECT id FROM ranked_records WHERE rn > 1
        )
        """
        
        conn.execute(cleanup_sql)
        print(f"✅ 清理完成")
        
        # 5. 验证结果
        print(f"\n5️⃣ 验证清理结果...")
        
        final_total = conn.execute(total_sql).fetchone()[0]
        final_duplicates = conn.execute(duplicate_sql).fetchone()[0]
        
        print(f"   清理后总记录数: {final_total:,}")
        print(f"   剩余重复组数: {final_duplicates:,}")
        print(f"   实际删除记录数: {total_count - final_total:,}")
        
        if final_duplicates == 0:
            print("✅ 重复数据清理成功！")
        else:
            print(f"⚠️  仍有 {final_duplicates} 组重复数据")
        
        # 6. 显示清理统计
        print(f"\n📊 清理统计:")
        print(f"   原始记录数: {total_count:,}")
        print(f"   删除记录数: {total_count - final_total:,}")
        print(f"   保留记录数: {final_total:,}")
        print(f"   数据压缩率: {((total_count - final_total) / total_count * 100):.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def show_sample_duplicates():
    """显示重复数据样本"""
    db_path = 'data/risk_analysis.duckdb'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 显示重复数据样本")
    print("=" * 80)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 查找重复数据样本
        sample_sql = """
        WITH duplicate_groups AS (
            SELECT 
                algorithm_result_id, member_id, contract_name, detection_type, detection_method,
                COUNT(*) as count
            FROM contract_risk_details
            GROUP BY algorithm_result_id, member_id, contract_name, detection_type, detection_method
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 5
        )
        SELECT 
            crd.id,
            crd.algorithm_result_id,
            crd.member_id,
            crd.contract_name,
            crd.detection_type,
            crd.detection_method,
            crd.risk_score,
            crd.created_at,
            dg.count as duplicate_count
        FROM contract_risk_details crd
        JOIN duplicate_groups dg ON (
            crd.algorithm_result_id = dg.algorithm_result_id AND
            crd.member_id = dg.member_id AND
            crd.contract_name = dg.contract_name AND
            crd.detection_type = dg.detection_type AND
            crd.detection_method = dg.detection_method
        )
        ORDER BY dg.count DESC, crd.algorithm_result_id, crd.created_at DESC
        """
        
        results = conn.execute(sample_sql).fetchall()
        
        if not results:
            print("✅ 没有重复数据")
            conn.close()
            return
        
        print(f"📋 重复数据样本 (前5组):")
        print("-" * 120)
        print(f"{'ID':<8} {'算法ID':<8} {'用户ID':<15} {'合约':<12} {'检测类型':<15} {'风险分数':<8} {'创建时间':<20} {'重复数'}")
        print("-" * 120)
        
        current_group = None
        for row in results:
            group_key = (row[1], row[2], row[3], row[4], row[5])  # algorithm_result_id, member_id, contract_name, detection_type, detection_method
            
            if current_group != group_key:
                if current_group is not None:
                    print("-" * 120)
                current_group = group_key
            
            user_id = str(row[2])[:13] + "..." if len(str(row[2])) > 13 else str(row[2])
            contract = str(row[3])[:10] + "..." if len(str(row[3])) > 10 else str(row[3])
            detection_type = str(row[4])[:13] + "..." if len(str(row[4])) > 13 else str(row[4])
            created_at = str(row[7])[:19] if row[7] else "N/A"
            
            print(f"{row[0]:<8} {row[1]:<8} {user_id:<15} {contract:<12} {detection_type:<15} {row[6]:<8.4f} {created_at:<20} {row[8]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 contract_risk_details表重复数据清理工具")
    print("=" * 60)
    
    while True:
        print(f"\n请选择操作:")
        print(f"1. 显示重复数据样本")
        print(f"2. 清理重复数据")
        print(f"3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            show_sample_duplicates()
        elif choice == '2':
            clean_contract_risk_duplicates()
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
