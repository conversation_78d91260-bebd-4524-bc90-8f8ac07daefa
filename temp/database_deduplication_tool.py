#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合约风险分析数据库去重工具
用于处理增量模式下的重复数据问题

功能：
1. 检测各表中的重复数据
2. 智能去重（保留最新或最完整的记录）
3. 生成去重报告
4. 备份原始数据

作者：系统管理员
创建时间：2025-08-05
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database.duckdb_manager import db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('temp/deduplication.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseDeduplicationTool:
    """数据库去重工具"""
    
    def __init__(self):
        self.db_manager = db_manager
        self.tables_config = {
            'contract_risk_details': {
                'unique_fields': ['algorithm_result_id', 'member_id', 'contract_name', 'detection_type', 'detection_method'],
                'priority_fields': ['created_at', 'risk_score'],  # 用于选择保留哪条记录
                'priority_order': 'DESC'  # DESC表示保留最新的
            },
            'same_account_wash_trading': {
                'unique_fields': ['user_id', 'contract_name', 'long_position_id', 'short_position_id'],
                'priority_fields': ['created_at'],
                'priority_order': 'DESC'
            },
            'cross_account_wash_trading': {
                'unique_fields': ['user_a_id', 'user_b_id', 'contract_name', 'trade_a_id', 'trade_b_id'],
                'priority_fields': ['created_at'],
                'priority_order': 'DESC'
            },
            'high_frequency_trading_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'priority_fields': ['created_at', 'trade_count'],
                'priority_order': 'DESC'
            },
            'funding_rate_arbitrage_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'priority_fields': ['created_at', 'estimated_profit'],
                'priority_order': 'DESC'
            }
        }
        
    def analyze_duplicates(self) -> Dict[str, Any]:
        """分析所有表的重复数据情况"""
        logger.info("🔍 开始分析重复数据...")
        
        analysis_result = {
            'timestamp': datetime.now().isoformat(),
            'tables': {},
            'summary': {
                'total_tables': len(self.tables_config),
                'tables_with_duplicates': 0,
                'total_duplicates': 0,
                'total_records': 0
            }
        }
        
        for table_name, config in self.tables_config.items():
            logger.info(f"分析表: {table_name}")
            table_analysis = self._analyze_table_duplicates(table_name, config)
            analysis_result['tables'][table_name] = table_analysis
            
            # 更新汇总信息
            analysis_result['summary']['total_records'] += table_analysis['total_records']
            analysis_result['summary']['total_duplicates'] += table_analysis['duplicate_count']
            if table_analysis['duplicate_count'] > 0:
                analysis_result['summary']['tables_with_duplicates'] += 1
        
        # 保存分析报告
        report_path = f"temp/duplicate_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 分析完成，报告已保存到: {report_path}")
        return analysis_result
    
    def _analyze_table_duplicates(self, table_name: str, config: Dict) -> Dict[str, Any]:
        """分析单个表的重复数据"""
        try:
            # 检查表是否存在
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            result = self.db_manager.execute_sql(check_sql)
            if not result:
                return {
                    'total_records': 0,
                    'duplicate_count': 0,
                    'duplicate_groups': [],
                    'error': f'表 {table_name} 不存在'
                }
            
            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM {table_name}"
            total_result = self.db_manager.execute_sql(count_sql)
            total_records = total_result[0]['total'] if total_result else 0
            
            if total_records == 0:
                return {
                    'total_records': 0,
                    'duplicate_count': 0,
                    'duplicate_groups': []
                }
            
            # 查找重复数据
            unique_fields = config['unique_fields']
            unique_fields_str = ', '.join(unique_fields)
            
            duplicate_sql = f"""
            SELECT {unique_fields_str}, COUNT(*) as count
            FROM {table_name}
            GROUP BY {unique_fields_str}
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            """
            
            duplicates = self.db_manager.execute_sql(duplicate_sql)
            duplicate_count = sum(dup['count'] - 1 for dup in duplicates)  # 减1因为每组保留一条
            
            return {
                'total_records': total_records,
                'duplicate_count': duplicate_count,
                'duplicate_groups': len(duplicates),
                'duplicate_details': duplicates[:10]  # 只保存前10个重复组的详情
            }
            
        except Exception as e:
            logger.error(f"分析表 {table_name} 失败: {e}")
            return {
                'total_records': 0,
                'duplicate_count': 0,
                'duplicate_groups': [],
                'error': str(e)
            }
    
    def remove_duplicates(self, dry_run: bool = True) -> Dict[str, Any]:
        """去除重复数据"""
        logger.info(f"🧹 开始去重操作 (dry_run={dry_run})...")
        
        removal_result = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'tables': {},
            'summary': {
                'total_removed': 0,
                'tables_processed': 0,
                'errors': []
            }
        }
        
        # 如果不是dry_run，先备份数据
        if not dry_run:
            backup_path = self._backup_database()
            removal_result['backup_path'] = backup_path
            logger.info(f"📦 数据已备份到: {backup_path}")
        
        for table_name, config in self.tables_config.items():
            logger.info(f"处理表: {table_name}")
            table_result = self._remove_table_duplicates(table_name, config, dry_run)
            removal_result['tables'][table_name] = table_result
            
            removal_result['summary']['total_removed'] += table_result.get('removed_count', 0)
            removal_result['summary']['tables_processed'] += 1
            
            if 'error' in table_result:
                removal_result['summary']['errors'].append(f"{table_name}: {table_result['error']}")
        
        # 保存去重报告
        report_path = f"temp/deduplication_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(removal_result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 去重完成，报告已保存到: {report_path}")
        return removal_result
    
    def _remove_table_duplicates(self, table_name: str, config: Dict, dry_run: bool) -> Dict[str, Any]:
        """去除单个表的重复数据"""
        try:
            unique_fields = config['unique_fields']
            priority_fields = config['priority_fields']
            priority_order = config['priority_order']
            
            # 构建查询语句，找出需要删除的重复记录
            unique_fields_str = ', '.join(unique_fields)
            priority_fields_str = ', '.join(priority_fields)
            
            # 使用窗口函数标记重复记录
            find_duplicates_sql = f"""
            WITH ranked_records AS (
                SELECT id, 
                       ROW_NUMBER() OVER (
                           PARTITION BY {unique_fields_str} 
                           ORDER BY {priority_fields_str} {priority_order}
                       ) as rn
                FROM {table_name}
            )
            SELECT id FROM ranked_records WHERE rn > 1
            """
            
            duplicate_ids = self.db_manager.execute_sql(find_duplicates_sql)
            
            if not duplicate_ids:
                return {
                    'removed_count': 0,
                    'message': '没有发现重复数据'
                }
            
            ids_to_remove = [str(record['id']) for record in duplicate_ids]
            
            if dry_run:
                return {
                    'removed_count': len(ids_to_remove),
                    'message': f'发现 {len(ids_to_remove)} 条重复记录（dry_run模式，未实际删除）',
                    'sample_ids': ids_to_remove[:10]
                }
            
            # 实际删除重复记录
            if ids_to_remove:
                ids_str = ', '.join(ids_to_remove)
                delete_sql = f"DELETE FROM {table_name} WHERE id IN ({ids_str})"
                self.db_manager.execute_sql(delete_sql)
                
                logger.info(f"✅ 表 {table_name} 删除了 {len(ids_to_remove)} 条重复记录")
            
            return {
                'removed_count': len(ids_to_remove),
                'message': f'成功删除 {len(ids_to_remove)} 条重复记录'
            }
            
        except Exception as e:
            logger.error(f"去重表 {table_name} 失败: {e}")
            return {
                'removed_count': 0,
                'error': str(e)
            }
    
    def _backup_database(self) -> str:
        """备份数据库"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"temp/database_backup_{timestamp}.db"
        
        # 这里简化处理，实际应该根据具体的数据库类型进行备份
        logger.info(f"数据库备份功能需要根据实际数据库类型实现")
        return backup_path

def main():
    """主函数"""
    print("🔧 合约风险分析数据库去重工具")
    print("=" * 50)
    
    tool = DatabaseDeduplicationTool()
    
    # 1. 分析重复数据
    print("\n1️⃣ 分析重复数据...")
    analysis = tool.analyze_duplicates()
    
    print(f"\n📊 分析结果:")
    print(f"   总表数: {analysis['summary']['total_tables']}")
    print(f"   有重复数据的表: {analysis['summary']['tables_with_duplicates']}")
    print(f"   总记录数: {analysis['summary']['total_records']}")
    print(f"   重复记录数: {analysis['summary']['total_duplicates']}")
    
    if analysis['summary']['total_duplicates'] == 0:
        print("✅ 没有发现重复数据，无需去重！")
        return
    
    # 2. 显示详细信息
    print(f"\n📋 各表重复情况:")
    for table_name, table_info in analysis['tables'].items():
        if table_info['duplicate_count'] > 0:
            print(f"   {table_name}: {table_info['duplicate_count']} 条重复记录")
    
    # 3. 询问是否执行去重
    print(f"\n2️⃣ 去重操作...")
    response = input("是否执行去重？(y/N): ").strip().lower()
    
    if response == 'y':
        # 先执行dry_run
        print("执行预演模式...")
        dry_result = tool.remove_duplicates(dry_run=True)
        
        print(f"预演结果: 将删除 {dry_result['summary']['total_removed']} 条重复记录")
        
        confirm = input("确认执行实际去重？(y/N): ").strip().lower()
        if confirm == 'y':
            print("执行实际去重...")
            result = tool.remove_duplicates(dry_run=False)
            print(f"✅ 去重完成！删除了 {result['summary']['total_removed']} 条重复记录")
        else:
            print("❌ 用户取消了去重操作")
    else:
        print("❌ 用户取消了去重操作")

if __name__ == "__main__":
    main()
