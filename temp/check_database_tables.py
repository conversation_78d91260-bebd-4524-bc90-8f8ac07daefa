#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的所有表
"""

import os
import duckdb

def check_database_tables():
    """检查数据库中的所有表"""
    db_path = 'backend/database/trading_analysis.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 检查数据库: {db_path}")
    print("=" * 60)
    
    try:
        conn = duckdb.connect(db_path)
        
        # 查看所有表
        tables_sql = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        result = conn.execute(tables_sql)
        tables = result.fetchall()
        
        print(f"📊 数据库中的表 (共 {len(tables)} 个):")
        print("-" * 40)
        
        for i, (table_name,) in enumerate(tables, 1):
            # 获取表的记录数
            try:
                count_sql = f"SELECT COUNT(*) FROM {table_name}"
                count_result = conn.execute(count_sql)
                count = count_result.fetchone()[0]
                print(f"{i:2d}. {table_name:<30} ({count:,} 条记录)")
            except Exception as e:
                print(f"{i:2d}. {table_name:<30} (查询失败: {e})")
        
        # 特别检查合约风险分析相关的表
        print(f"\n🔍 检查合约风险分析相关表:")
        print("-" * 40)
        
        risk_tables = [
            'contract_risk_analysis',
            'contract_risk_details', 
            'algorithm_results',
            'same_account_wash_trading',
            'cross_account_wash_trading',
            'high_frequency_trading_details',
            'funding_rate_arbitrage_details',
            'wash_trading_results',
            'wash_trading_pairs'
        ]
        
        for table in risk_tables:
            try:
                check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'"
                result = conn.execute(check_sql)
                exists = result.fetchone()
                
                if exists:
                    count_sql = f"SELECT COUNT(*) FROM {table}"
                    count_result = conn.execute(count_sql)
                    count = count_result.fetchone()[0]
                    print(f"✅ {table:<35} ({count:,} 条记录)")
                    
                    # 如果有数据，显示最近的几条记录的时间
                    if count > 0:
                        try:
                            recent_sql = f"SELECT created_at FROM {table} ORDER BY created_at DESC LIMIT 1"
                            recent_result = conn.execute(recent_sql)
                            recent_time = recent_result.fetchone()
                            if recent_time:
                                print(f"   最新记录时间: {recent_time[0]}")
                        except:
                            pass
                else:
                    print(f"❌ {table:<35} (不存在)")
            except Exception as e:
                print(f"⚠️  {table:<35} (检查失败: {e})")
        
        # 如果contract_risk_analysis表存在，检查其结构
        if any(table[0] == 'contract_risk_analysis' for table in tables):
            print(f"\n📋 contract_risk_analysis表结构:")
            print("-" * 40)
            try:
                pragma_sql = "PRAGMA table_info(contract_risk_analysis)"
                result = conn.execute(pragma_sql)
                columns = result.fetchall()
                
                for col in columns:
                    print(f"  {col[1]:<20} {col[2]:<15} {'NOT NULL' if col[3] else 'NULL':<8} {f'DEFAULT {col[4]}' if col[4] else ''}")
            except Exception as e:
                print(f"获取表结构失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_tables()
