# 合约风险分析数据库去重配置
# 用于配置各表的去重策略和规则

# 全局配置
global:
  # 默认去重策略
  default_strategy: "keep_latest"  # keep_latest, keep_highest_score, keep_most_complete
  
  # 备份设置
  backup_enabled: true
  backup_path: "temp/backups"
  
  # 日志设置
  log_level: "INFO"
  log_file: "temp/deduplication.log"

# 表级配置
tables:
  # 通用风险事件详情表
  contract_risk_details:
    # 唯一性字段组合
    unique_fields:
      - algorithm_result_id
      - member_id
      - contract_name
      - detection_type
      - detection_method
    
    # 去重策略
    deduplication_strategy: "keep_latest"
    
    # 优先级字段（用于决定保留哪条记录）
    priority_fields:
      - created_at: "DESC"  # 保留最新的
      - risk_score: "DESC"  # 风险分数高的优先
    
    # 数据完整性检查字段
    completeness_fields:
      - risk_score
      - abnormal_volume
      - trade_count
      - time_range
    
    # 特殊处理规则
    special_rules:
      # 如果detection_method为空，尝试从detection_type推断
      infer_detection_method: true
      
      # 合并additional_data字段
      merge_additional_data: true

  # 同账户对敲详情表
  same_account_wash_trading:
    unique_fields:
      - user_id
      - contract_name
      - long_position_id
      - short_position_id
    
    deduplication_strategy: "keep_latest"
    
    priority_fields:
      - created_at: "DESC"
      - time_gap: "ASC"  # 时间间隔小的优先（更可疑）
    
    completeness_fields:
      - long_open_time
      - short_open_time
      - long_volume
      - short_volume
    
    special_rules:
      # 如果position_id为空，使用时间戳生成
      generate_missing_position_id: true

  # 跨账户对敲详情表
  cross_account_wash_trading:
    unique_fields:
      - user_a_id
      - user_b_id
      - contract_name
      - trade_a_id
      - trade_b_id
    
    deduplication_strategy: "keep_latest"
    
    priority_fields:
      - created_at: "DESC"
      - time_correlation: "DESC"  # 时间相关性高的优先
      - volume_correlation: "DESC"  # 成交量相关性高的优先
    
    completeness_fields:
      - trade_a_time
      - trade_b_time
      - trade_a_volume
      - trade_b_volume
      - bd_relationship
    
    special_rules:
      # 标准化用户ID顺序（确保user_a_id < user_b_id）
      normalize_user_order: true
      
      # 推断BD关系
      infer_bd_relationship: true

  # 高频交易详情表
  high_frequency_trading_details:
    unique_fields:
      - algorithm_result_id
      - user_id
      - contract_name
    
    deduplication_strategy: "keep_highest_score"
    
    priority_fields:
      - trade_count: "DESC"  # 交易次数多的优先
      - max_frequency: "DESC"  # 最大频率高的优先
      - created_at: "DESC"
    
    completeness_fields:
      - trade_count
      - max_frequency
      - time_pattern
      - risk_indicators
    
    special_rules:
      # 合并风险指标
      merge_risk_indicators: true
      
      # 重新计算频率指标
      recalculate_frequency: true

  # 资金费率套利详情表
  funding_rate_arbitrage_details:
    unique_fields:
      - algorithm_result_id
      - user_id
      - contract_name
    
    deduplication_strategy: "keep_highest_profit"
    
    priority_fields:
      - estimated_profit: "DESC"  # 预估收益高的优先
      - position_size: "DESC"  # 仓位大的优先
      - created_at: "DESC"
    
    completeness_fields:
      - funding_rate
      - position_size
      - holding_duration
      - estimated_profit
      - market_conditions
    
    special_rules:
      # 合并市场条件数据
      merge_market_conditions: true
      
      # 重新计算收益指标
      recalculate_profit_metrics: true

# 去重策略定义
strategies:
  keep_latest:
    description: "保留最新的记录"
    sort_field: "created_at"
    sort_order: "DESC"
  
  keep_highest_score:
    description: "保留风险分数最高的记录"
    sort_field: "risk_score"
    sort_order: "DESC"
    fallback_field: "created_at"
    fallback_order: "DESC"
  
  keep_most_complete:
    description: "保留数据最完整的记录"
    completeness_weight: 0.7
    recency_weight: 0.3
  
  keep_highest_profit:
    description: "保留预估收益最高的记录"
    sort_field: "estimated_profit"
    sort_order: "DESC"
    fallback_field: "created_at"
    fallback_order: "DESC"

# 数据质量检查规则
quality_checks:
  # 必填字段检查
  required_fields:
    contract_risk_details:
      - algorithm_result_id
      - member_id
      - detection_type
    
    same_account_wash_trading:
      - user_id
      - contract_name
    
    cross_account_wash_trading:
      - user_a_id
      - user_b_id
      - contract_name
    
    high_frequency_trading_details:
      - algorithm_result_id
      - user_id
      - contract_name
    
    funding_rate_arbitrage_details:
      - algorithm_result_id
      - user_id
      - contract_name

  # 数据格式检查
  format_checks:
    # 时间戳格式
    timestamp_fields:
      - created_at
      - long_open_time
      - short_open_time
      - trade_a_time
      - trade_b_time
    
    # 数值范围检查
    numeric_ranges:
      risk_score:
        min: 0
        max: 1
      
      time_correlation:
        min: 0
        max: 1
      
      volume_correlation:
        min: 0
        max: 1
      
      price_correlation:
        min: 0
        max: 1

# 性能优化配置
performance:
  # 批处理大小
  batch_size: 1000
  
  # 并发处理
  max_workers: 4
  
  # 内存限制（MB）
  memory_limit: 512
  
  # 临时表前缀
  temp_table_prefix: "dedup_temp_"

# 报告配置
reporting:
  # 生成详细报告
  detailed_report: true
  
  # 报告格式
  report_formats:
    - json
    - html
  
  # 报告保存路径
  report_path: "temp/reports"
  
  # 包含样本数据
  include_samples: true
  sample_size: 10
