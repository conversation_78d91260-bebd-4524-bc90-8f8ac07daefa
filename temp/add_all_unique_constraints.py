#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有合约风险分析相关表添加唯一性约束
"""

import os
import duckdb
from datetime import datetime

class TableConstraintManager:
    """表约束管理器"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = None
        
        # 定义所有需要添加约束的表及其约束字段
        self.tables_config = {
            'contract_risk_details': {
                'unique_fields': ['algorithm_result_id', 'member_id', 'contract_name', 'detection_type', 'detection_method'],
                'description': '合约风险详情表'
            },
            'same_account_wash_trading': {
                'unique_fields': ['user_id', 'contract_name', 'long_position_id', 'short_position_id'],
                'description': '同账户对敲详情表'
            },
            'cross_account_wash_trading': {
                'unique_fields': ['user_a_id', 'user_b_id', 'contract_name', 'trade_a_id', 'trade_b_id'],
                'description': '跨账户对敲详情表'
            },
            'high_frequency_trading_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'description': '高频交易详情表'
            },
            'funding_rate_arbitrage_details': {
                'unique_fields': ['algorithm_result_id', 'user_id', 'contract_name'],
                'description': '资金费率套利详情表'
            }
        }
    
    def connect(self):
        """连接数据库"""
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")
        self.conn = duckdb.connect(self.db_path)
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
    
    def table_exists(self, table_name):
        """检查表是否存在"""
        try:
            check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            result = self.conn.execute(check_sql).fetchone()
            return result is not None
        except:
            return False
    
    def get_table_structure(self, table_name):
        """获取表结构"""
        try:
            pragma_sql = f"PRAGMA table_info({table_name})"
            result = self.conn.execute(pragma_sql)
            return result.fetchall()
        except Exception as e:
            print(f"获取表 {table_name} 结构失败: {e}")
            return []
    
    def get_create_statement(self, table_name):
        """获取建表语句"""
        try:
            sql = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            result = self.conn.execute(sql).fetchone()
            return result[0] if result else None
        except:
            return None
    
    def check_duplicates(self, table_name, unique_fields):
        """检查重复数据"""
        try:
            unique_fields_str = ', '.join(unique_fields)
            duplicate_sql = f"""
            SELECT COUNT(*) as duplicate_groups,
                   SUM(count - 1) as duplicate_records
            FROM (
                SELECT {unique_fields_str}, COUNT(*) as count
                FROM {table_name}
                GROUP BY {unique_fields_str}
                HAVING COUNT(*) > 1
            ) duplicates
            """
            
            result = self.conn.execute(duplicate_sql).fetchone()
            return {
                'duplicate_groups': result[0] or 0,
                'duplicate_records': result[1] or 0
            }
        except Exception as e:
            print(f"检查表 {table_name} 重复数据失败: {e}")
            return {'duplicate_groups': 0, 'duplicate_records': 0}
    
    def clean_duplicates(self, table_name, unique_fields):
        """清理重复数据"""
        try:
            unique_fields_str = ', '.join(unique_fields)
            
            # 使用ROW_NUMBER()窗口函数删除重复记录，保留最新的
            cleanup_sql = f"""
            DELETE FROM {table_name}
            WHERE id IN (
                WITH ranked_records AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY {unique_fields_str}
                               ORDER BY created_at DESC, id DESC
                           ) as rn
                    FROM {table_name}
                )
                SELECT id FROM ranked_records WHERE rn > 1
            )
            """
            
            self.conn.execute(cleanup_sql)
            return True
        except Exception as e:
            print(f"清理表 {table_name} 重复数据失败: {e}")
            return False
    
    def add_unique_constraint(self, table_name, unique_fields):
        """为表添加唯一性约束"""
        try:
            print(f"\n🔧 处理表: {table_name}")
            print("-" * 50)
            
            # 1. 检查表是否存在
            if not self.table_exists(table_name):
                print(f"⚠️  表 {table_name} 不存在，跳过")
                return False
            
            # 2. 获取当前记录数
            count_sql = f"SELECT COUNT(*) FROM {table_name}"
            total_count = self.conn.execute(count_sql).fetchone()[0]
            print(f"当前记录数: {total_count:,}")
            
            if total_count == 0:
                print("表为空，跳过处理")
                return True
            
            # 3. 检查重复数据
            duplicate_info = self.check_duplicates(table_name, unique_fields)
            print(f"重复组数: {duplicate_info['duplicate_groups']:,}")
            print(f"重复记录数: {duplicate_info['duplicate_records']:,}")
            
            # 4. 清理重复数据（如果有）
            if duplicate_info['duplicate_records'] > 0:
                print("🧹 清理重复数据...")
                if self.clean_duplicates(table_name, unique_fields):
                    # 验证清理结果
                    final_count = self.conn.execute(count_sql).fetchone()[0]
                    cleaned_count = total_count - final_count
                    print(f"✅ 清理完成: 删除了 {cleaned_count:,} 条重复记录")
                    total_count = final_count
                else:
                    print("❌ 清理失败")
                    return False
            
            # 5. 创建备份表
            backup_table = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_sql = f"CREATE TABLE {backup_table} AS SELECT * FROM {table_name}"
            self.conn.execute(backup_sql)
            print(f"📦 备份表已创建: {backup_table}")
            
            # 6. 获取原表结构
            columns = self.get_table_structure(table_name)
            if not columns:
                print("❌ 无法获取表结构")
                return False
            
            # 7. 构建新表的CREATE语句
            new_table = f"{table_name}_new"
            column_defs = []
            
            for col in columns:
                cid, name, type_name, notnull, dflt_value, pk = col
                col_def = f"{name} {type_name}"
                
                if pk:
                    col_def += " PRIMARY KEY"
                elif notnull:
                    col_def += " NOT NULL"
                
                if dflt_value is not None:
                    if dflt_value == 'CURRENT_TIMESTAMP':
                        col_def += " DEFAULT CURRENT_TIMESTAMP"
                    elif dflt_value == 'NULL':
                        col_def += " DEFAULT NULL"
                    else:
                        col_def += f" DEFAULT {dflt_value}"
                
                column_defs.append(col_def)
            
            # 添加唯一性约束
            unique_constraint = f"UNIQUE({', '.join(unique_fields)})"
            column_defs.append(unique_constraint)
            
            column_defs_str = ',\n    '.join(column_defs)
            create_new_sql = f"""
            CREATE TABLE {new_table} (
                {column_defs_str}
            )
            """
            
            print("🏗️  创建新表结构...")
            self.conn.execute(create_new_sql)
            
            # 8. 复制数据到新表
            print("📋 复制数据...")
            column_names = [col[1] for col in columns]
            copy_sql = f"""
            INSERT INTO {new_table} ({', '.join(column_names)})
            SELECT {', '.join(column_names)}
            FROM {table_name}
            """
            
            self.conn.execute(copy_sql)
            
            # 验证数据复制
            new_count = self.conn.execute(f"SELECT COUNT(*) FROM {new_table}").fetchone()[0]
            if new_count != total_count:
                print(f"❌ 数据复制不完整: 原始{total_count:,} vs 新表{new_count:,}")
                self.conn.execute(f"DROP TABLE {new_table}")
                return False
            
            # 9. 替换原表
            print("🔄 替换原表...")
            self.conn.execute(f"DROP TABLE {table_name}")
            self.conn.execute(f"ALTER TABLE {new_table} RENAME TO {table_name}")
            
            # 10. 验证唯一性约束
            print("🧪 验证唯一性约束...")
            sample_sql = f"SELECT {', '.join(unique_fields)} FROM {table_name} LIMIT 1"
            sample = self.conn.execute(sample_sql).fetchone()
            
            if sample:
                # 构建测试插入语句
                placeholders = ', '.join(['?' for _ in unique_fields])
                test_columns = unique_fields + ['created_at']
                test_values = list(sample) + ['CURRENT_TIMESTAMP']
                
                test_insert_sql = f"""
                INSERT INTO {table_name} ({', '.join(test_columns)})
                VALUES ({placeholders}, CURRENT_TIMESTAMP)
                """
                
                try:
                    self.conn.execute(test_insert_sql, sample)
                    print("❌ 唯一性约束未生效")
                    return False
                except Exception as e:
                    print(f"✅ 唯一性约束生效: {str(e)[:100]}...")
            
            print(f"✅ 表 {table_name} 处理完成")
            return True
            
        except Exception as e:
            print(f"❌ 处理表 {table_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_all_tables(self):
        """处理所有表"""
        print("🚀 开始为所有表添加唯一性约束")
        print("=" * 80)
        
        results = {}
        
        for table_name, config in self.tables_config.items():
            unique_fields = config['unique_fields']
            description = config['description']
            
            print(f"\n📋 {description} ({table_name})")
            print(f"唯一性字段: {', '.join(unique_fields)}")
            
            success = self.add_unique_constraint(table_name, unique_fields)
            results[table_name] = success
        
        # 显示总结
        print(f"\n" + "=" * 80)
        print("📊 处理结果总结:")
        print("-" * 40)
        
        success_count = 0
        for table_name, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{table_name:<35} {status}")
            if success:
                success_count += 1
        
        print(f"\n总计: {success_count}/{len(results)} 个表处理成功")
        
        if success_count == len(results):
            print("🎉 所有表的唯一性约束添加完成！")
        else:
            print("⚠️  部分表处理失败，请检查错误信息")

def main():
    """主函数"""
    db_path = 'data/risk_analysis.duckdb'
    
    print("🔧 合约风险分析表唯一性约束添加工具")
    print("=" * 80)
    print("将为以下表添加唯一性约束:")
    print("1. contract_risk_details")
    print("2. same_account_wash_trading") 
    print("3. cross_account_wash_trading")
    print("4. high_frequency_trading_details")
    print("5. funding_rate_arbitrage_details")
    print()
    print("⚠️  注意事项:")
    print("- 此操作会重建表结构")
    print("- 会自动清理重复数据")
    print("- 会创建备份表")
    print("- 操作不可逆，请确保已备份重要数据")
    
    response = input("\n是否继续？(y/N): ").strip().lower()
    if response != 'y':
        print("❌ 用户取消操作")
        return
    
    manager = TableConstraintManager(db_path)
    
    try:
        manager.connect()
        manager.process_all_tables()
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        manager.disconnect()

if __name__ == "__main__":
    main()
